import XCTest
@testable import IngredientScanner

@MainActor
final class ViewStateTests: XCTestCase {
    
    // Simple stub for RecipeGenerationService to control success/failure
    actor StubRecipeGenerationService: RecipeGenerationServiceProtocol {
        enum Mode { case success([RecipeIdea]), failure(Error) }
        var mode: Mode
        init(mode: Mode) { self.mode = mode }
        func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
            switch mode {
            case .success(let ideas): return ideas
            case .failure(let error): throw error
            }
        }
    }

    // Lightweight pantry service with items
    @MainActor
    final class StubPantryService: PantryService {
        init(items: [Ingredient]) {
            super.init()
            self.pantryItems = items
        }
    }

    func makeVM(pantryCount: Int = 1, stubService: StubRecipeGenerationService) -> RecipeGeneratorViewModel {
        // Seed pantry
        ServiceContainer.shared.pantryService.pantryItems = (0..<pantryCount).map { _ in Ingredient(name: "Apple", category: .fruit) }
        return RecipeGeneratorViewModel(recipeService: stubService,
                                        authService: ServiceContainer.shared.authenticationService)
    }

    func test_idle_to_loading_on_generate() async {
        let stub = StubRecipeGenerationService(mode: .success([]))
        let vm = makeVM(pantryCount: 2, stubService: stub)
        XCTAssertEqual(vm.viewState, .idle)
        await vm.generateRecipeIdeas(cookingTimeMinutes: 20)
        // After call returns, it should be loaded (success path), but ensure it passed through loading by verifying not idle
        XCTAssertNotEqual(vm.viewState, .idle)
    }

    func test_loading_to_loaded_on_success() async {
        // Prepare one idea
        let recipe = Recipe(recipeTitle: "Pasta", description: "Tasty", ingredients: ["pasta"], instructions: ["cook"], nutrition: .init(calories: "200", protein: "10g", carbs: "30g", fat: "5g"), cookingTime: "20 minutes", servings: 2, difficulty: .easy)
        let idea = RecipeIdea(recipe: recipe, status: .readyToCook, missingIngredients: [])
        let stub = StubRecipeGenerationService(mode: .success([idea]))
        let vm = makeVM(pantryCount: 1, stubService: stub)

        await vm.generateRecipeIdeas(cookingTimeMinutes: 15)
        if case .loaded(let models) = vm.viewState {
            XCTAssertEqual(models.count, 1)
            XCTAssertEqual(models.first?.title, "Pasta")
        } else {
            XCTFail("Expected loaded state")
        }
    }

    func test_loading_to_failed_on_error() async {
        enum DummyError: Error { case test }
        let stub = StubRecipeGenerationService(mode: .failure(DummyError.test))
        let vm = makeVM(pantryCount: 1, stubService: stub)

        await vm.generateRecipeIdeas(cookingTimeMinutes: 10)
        if case .failed = vm.viewState {
            XCTAssertTrue(true)
        } else {
            XCTFail("Expected failed state")
        }
    }
}

