# V8 PRD — Meal Plan Generation Performance & Stability

## 1. Background
Meal Plan (Custom) generation can appear to “hang” because it performs multiple sequential network calls per meal type and prefetches details after each batch. Quick mode does not suffer from this because it does a single batch with a small prefetch. The investigation document (v8/meal-plan-generation-investigation-and-fixes.md) identifies excessive prefetch in Meal Plan and lack of parallelism as key causes.

## 2. Goals (What success looks like)
- Eliminate perceived “freeze” during Meal Plan generation.
- Reduce total AI calls for typical scenarios (e.g., Lunch+Dinner, 5 days) from up to 10 → 4.
- Keep the main thread responsive throughout generation; Cancel reacts promptly.
- Preserve generated plan content, slot assignment, and retention behavior.

## 3. Non‑Goals
- No change to Quick mode behavior (still prefetches details).
- No major redesign of plan data structures or UI navigation.
- No immediate change to recipe detail UI (details will load on demand for Meal Plan).

## 4. Users & Key Scenarios
- Primary user: home cook planning multiple days (2–3 meal types) with family size and time constraints.
- Core scenario for validation: 5 days; Lunch 3 dishes@40 min; Dinner 4 dishes@60 min; family size 5.

## 5. Scope
- In scope: generation pipeline behavior, concurrency adjustments, main-thread usage, persistence performance.
- Out of scope: new UI pages, new data models, server/API changes beyond concurrency pattern and prefetch flag.

## 6. Assumptions & Dependencies
- Downstream AI service can handle limited parallelism (cap if needed).
- User profile and pantry are available; same-day cutoff logic remains unchanged (Utils/MealCutoffManager.swift).
- PlanStore uses UserDefaults JSON; atomicity remains required.

## 7. Functional Requirements

### FR‑P0: Disable detail prefetch in Meal Plan path
- Add a parameter to `RecipeServiceAdapter.generate(…, prefetchDetails: Bool = true)`.
- In `StructuredMealPlanGenerator`, call `adapter.generate(…, prefetchDetails: false)`.
- Quick mode continues to call with default `true`.
- Acceptance:
  - Meal Plan generation performs 0 detail prefetch calls per meal type.
  - Generated plan content (counts, assignment, dates) is unchanged.

### FR‑P1: Parallelize per‑meal generation with a cap
- In `StructuredMealPlanGenerator.generatePlan`, run a task per selected meal type to call `adapter.generate` concurrently.
- Provide a small concurrency cap (2–3) if external rate limits are present; otherwise concurrent for selected meals.
- Errors in one meal type do not fail the whole plan (graceful empty for that meal type).
- Acceptance:
  - Lunch and Dinner batches run in parallel; total wall‑clock ≈ max(single meal batch times), not sum.
  - No data races; deterministic assignment to slots remains.

### FR‑P1: Keep MainActor responsive
- In `RecipeGeneratorViewModel.generateRecipes`, do not await the full generation task on MainActor.
- Only update `viewState` on MainActor; heavy work runs off MainActor.
- Acceptance: UI remains scrollable and Cancel responds during generation.

### FR‑P2: Offload PlanStore heavy work off MainActor
- Move merge and JSON encoding to a background task/actor; perform final write and UI notifications on MainActor.
- Acceptance: no observable main thread hitch when saving large plans.

### FR‑P2 (Optional): Prefetcher decoupling
- Extract prefetch logic into a `RecipeDetailPrefetcher` service for future tuning.
- No functional change in this release; groundwork for later.

## 8. Non‑Functional Requirements (NFR)
- Performance: Reduce perceived wait by 50%+ in the core scenario; AI calls drop to 4.
- Reliability: No crashes; partial failures per meal type are tolerated and surfaced gracefully.
- Concurrency safety: No data races; respect API rate limits with a small cap.
- Responsiveness: Main thread never blocks on long awaits.

## 9. UX & Copy (Minimal)
- Loading state remains; add “Cancel” affordance (existing) and ensure it is responsive.
- Optional (nice‑to‑have, not required): surface step hints like “Generating Lunch… / Dinner…” when parallelization is enabled.

## 10. Telemetry & Instrumentation
- Log timings:
  - ViewModel: enter/exit performGeneration; state transitions.
  - Adapter: enter/exit generate; tag prefetch enabled/disabled.
  - Prefetch: enter/exit (to measure old vs new cost).
- Metrics to capture per run: number of selected meals, total requested dishes, total AI calls, wall‑clock time.

## 11. Acceptance Criteria (Measurable)
- Core scenario (5 days, Lunch 3@40, Dinner 4@60):
  - BEFORE (baseline): up to 10 AI calls (2 idea gens + up to 3 prefetch per meal × 2 meals), serial.
  - AFTER (P0): exactly 4 AI calls (2 idea gens per meal × 2 meals), no prefetch.
  - AFTER (P0+P1): 4 AI calls and Lunch/Dinner run concurrently.
- UI: Cancel interrupts mid‑generation within 500 ms.
- Plan saved: overlap policy and 4‑week retention remain intact.
- No functional regressions in Quick mode.

## 12. Rollout & Config
- Guard with minimal risk:
  - `prefetchDetails` parameter default true; Structured path sets false explicitly.
  - Concurrency cap configurable via constant/remote flag if available.
- Staged rollout (if desired): enable parallelization behind a feature flag for internal testing first.

## 13. Risks & Mitigations
- Risk: Parallel calls hit rate limit.
  - Mitigation: cap concurrency (e.g., 2); stagger task starts by 200–300 ms if needed.
- Risk: Hidden dependency on prefetch for Meal Plan.
  - Mitigation: confirm detail views load on demand; add smoke test.
- Risk: MainActor offload could introduce save ordering issues.
  - Mitigation: keep final write atomic on MainActor; unit test merge correctness.

## 14. Open Questions
- Do we want progress granularity in UI (per‑meal progress) for Meal Plan?
- Should concurrency cap be configurable remotely (RemoteConfigurationManager)?
- Any A/B measurement windows for timing telemetry?

## 15. Milestones
- P0 (Day 1–2):
  - Add `prefetchDetails` flag; set false for Structured path
  - Unit test: ensure Meal Plan doesn’t prefetch; Quick still does
- P1 (Day 3–5):
  - Parallelize per‑meal generation with cap; smoke test no races, verify counts/assignment
  - ViewModel: stop awaiting full task on MainActor; verify Cancel responsiveness
- P2 (Day 6–7):
  - Offload PlanStore merge/encode; regression tests for overlap/retention
  - Optional: extract `RecipeDetailPrefetcher`

## 16. References
- Investigation & fixes: v8/meal-plan-generation-investigation-and-fixes.md
- Technical implementation guide: v8/README.md
- Key code paths:
  - Features/RecipeGenerator/RecipeGeneratorView.swift
  - Features/RecipeGenerator/RecipeGeneratorViewModel.swift
  - Services/StructuredMealPlanGenerator.swift
  - Services/RecipeServiceAdapter.swift
  - Services/RecipeGenerationService.swift
  - Services/PlanStore.swift
  - Utils/MealCutoffManager.swift



---

## 17. Detailed Specifications (PRD‑style, aligned to V7 format)

### 17.1 Executive Summary and Business Impact
- Problem: Meal Plan (Custom) generation appears to “hang” due to compounded network latency from per‑meal batches and post‑batch detail prefetching.
- Impact: Users abandon the feature; perceived instability hurts retention and app ratings.
- Solution: Disable detail prefetch in Meal Plan, introduce safe per‑meal parallelization, and keep the main thread free. Preserve Quick behavior.

### 17.2 Implementation Strategy — Phase‑based
1) P0 (Critical): Disable prefetch for Meal Plan path only; no UX/logic change otherwise.
2) P1 (High): Parallelize per‑meal generation with a small concurrency cap; avoid awaiting full task on MainActor.
3) P2 (Medium): Offload PlanStore merge/encode off MainActor; optional prefetcher decoupling for future tuning.

### 17.3 Detailed Requirements and Code Changes

#### P0 — Disable detail prefetch in Meal Plan (Low‑risk, highest impact)
- Root cause: `RecipeServiceAdapter.generate(...)` always prefetches top‑K details (see Services/RecipeServiceAdapter.swift ~lines 116–119), multiplying calls per meal type.
- Technical requirements:
  - Add `prefetchDetails: Bool = true` parameter to `RecipeServiceAdapter.generate(...)` (signature around line ~7).
  - Wrap the existing prefetch block with `if prefetchDetails { ... }` (around ~116–119).
  - In `StructuredMealPlanGenerator.generatePlan(...)`, pass `prefetchDetails: false` when calling adapter (call site around ~59).
- Code changes required:
  - File: Services/RecipeServiceAdapter.swift
    - Update function signature to include `prefetchDetails` with default `true`.
    - Guard prefetch with the new flag.
  - File: Services/StructuredMealPlanGenerator.swift
    - When calling `adapter.generate(...)` inside the per‑meal loop, add `prefetchDetails: false`.
- Acceptance:
  - Meal Plan path performs 0 detail prefetch calls per meal type.
  - Quick path remains unchanged (prefetch enabled by default).

#### P1 — Parallelize per‑meal generation (Wall‑clock reduction)
- Current: Serial loop over meals (Services/StructuredMealPlanGenerator.swift ~43–66).
- Technical requirements:
  - Use `withTaskGroup` to run `adapter.generate(...)` per selected meal concurrently.
  - Add a small concurrency cap (e.g., 2) if required by downstream API limits.
  - Continue graceful isolation: failure in one meal type must not fail the entire plan.
- Code changes required:
  - File: Services/StructuredMealPlanGenerator.swift
    - Replace serial loop with a `withTaskGroup(of: (MealType, [RecipeUIModel]).self)` pattern.
    - Insert results into `generatedByMeal[meal]` as tasks complete.
    - Keep subsequent slot assignment (`buildDayPlans`) intact.
- Acceptance:
  - With Lunch and Dinner selected, total time ≈ longer of the two batches (not sum).
  - No data races; slot assignment counts remain accurate.

#### P1 — Keep MainActor responsive (Reduce UI “freeze” risk)
- Current: `RecipeGeneratorViewModel.generateRecipes` awaits `task.value` on MainActor (Features/RecipeGenerator/RecipeGeneratorViewModel.swift ~155).
- Technical requirements:
  - Do not await the entire generation task on MainActor; only update `viewState` via MainActor when states change.
  - Preserve cancellability (existing `cancelGeneration` is kept).
- Code changes required:
  - File: Features/RecipeGenerator/RecipeGeneratorViewModel.swift
    - Remove `_ = await task.value` from `generateRecipes(...)` or gate it under non‑UI test builds.
- Acceptance:
  - While generating, scrolling/interaction remains responsive; Cancel reacts promptly.

#### P2 — Offload PlanStore merge/encode (Smooth post‑generation save)
- Current: `PlanStore` is `@MainActor` (Services/PlanStore.swift) and performs merge + JSON encoding on main thread.
- Technical requirements:
  - Perform merge and encoding in a background task/actor, then `await MainActor.run { defaults.set(...) }` for the final write and UI notifications.
- Code changes required:
  - File: Services/PlanStore.swift
    - Refactor `mergeAndSave(newPlan:)` internals to offload heavy work; keep atomic write and overlap summary semantics unchanged.
- Acceptance:
  - No visible hitch on save even for larger plans; overlap policy and retention unchanged.

#### P2 (Optional) — Prefetcher decoupling
- Create `RecipeDetailPrefetcher` to encapsulate detail prefetch; call only from Quick flows.
- No functional change required for V8; prepares for adaptive prefetch in future.

### 17.4 Affected Areas (Files)
- Features/RecipeGenerator/RecipeGeneratorViewModel.swift (MainActor awaiting; state updates; navigation)
- Services/StructuredMealPlanGenerator.swift (per‑meal batching; parallelism; call‑site prefetch flag)
- Services/RecipeServiceAdapter.swift (generate signature; guarded prefetch)
- Services/PlanStore.swift (merge/encode threading)
- Utils/MealCutoffManager.swift (no change; referenced logic)

### 17.5 Edge Cases & Rules
- Empty pantry → return empty plan (already handled; keep graceful behavior).
- Same‑day cutoff → skip past meals for “today” only; future days unaffected.
- Idea shortfall → single retry + filler fallback; enforce exact counts per meal batch.
- Partial failures → meal type may yield 0; others proceed; save still happens.
- Cancellation → check `Task.isCancelled` around awaits to abort quickly.

### 17.6 UX Notes
- Meal Plan will not prefetch details; detail screens fetch on demand.
- Optional: show per‑meal step labels during parallel generation (“Generating Lunch… / Dinner…”).
- Keep Cancel readily accessible; ensure no modal blocks it.

### 17.7 Telemetry (Expanded)
- Per run capture: selectedMeals count; total requested dishes; actual AI calls; wall‑clock (overall and per meal); prefetchEnabled flag.
- Emit timing markers at:
  - ViewModel.performGeneration enter/exit
  - Adapter.generate enter/exit (tag mealType and prefetch flag)
  - Prefetch enter/exit (legacy path only)
- Dashboard: compare BEFORE vs AFTER (P0 then P1) on the core scenario.

### 17.8 Testing Matrix
- Unit tests:
  - Assert Meal Plan path never triggers prefetch; Quick path does (spy or injected flag capture).
  - Parallelization smoke test: generate for {Lunch, Dinner} → counts and slot assignment match serial baseline.
  - PlanStore merge correctness unchanged (overlap summary; 4‑week retention).
- Integration tests:
  - Scenario: 5 days, Lunch 3×@40, Dinner 4×@60, family size 5 → AI calls drop from ≤10 to 4; time decreases.
  - Cancel mid‑generation → completes cancellation within 500 ms; no leaked state.
- Device tests:
  - iOS 17+ simulators and at least one physical device.

### 17.9 Rollout & Config Details
- Default behavior: `prefetchDetails` = true; Structured path explicitly passes false.
- Concurrency cap: constant with option to wire to RemoteConfigurationManager (future).
- Feature flag (optional): gate parallelization for internal testing.

### 17.10 Risks (Expanded) & Mitigations
- Rate limits under parallelism → cap ≤2 and/or stagger starts by 200–300 ms.
- Hidden reliance on prefetch → verify detail views fetch on demand; add on‑demand cache warm.
- Save re‑ordering risk when offloading → keep final write on MainActor; add unit test for merge determinism.

### 17.11 Timeline & Ownership
- P0 (Day 1–2): Adapter flag + Structured call‑site change; unit tests
- P1 (Day 3–5): TaskGroup parallelization + ViewModel await removal; smoke tests
- P2 (Day 6–7): PlanStore offload; regression tests; optional prefetcher extraction
- Owner: iOS Core (Generator) team; Reviewer: Services lead

### 17.12 Acceptance Criteria (Expanded)
- Calls: Lunch+Dinner@5 days → BEFORE ≤10; AFTER P0 exactly 4; AFTER P1 still 4, faster wall‑clock.
- UI: Main thread stays responsive; Cancel < 500 ms.
- Persistence: Overlap policy and 4‑week retention pass previous test suite unchanged.
- Quick: No behavior change; still prefetches details; toast remains snappy.
