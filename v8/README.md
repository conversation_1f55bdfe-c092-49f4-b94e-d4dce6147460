# V8 Meal Plan Generation Optimization Guide

## Overview

This folder documents the V8 implementation plan to eliminate "hangs" during Meal Plan generation and improve overall responsiveness. An engineer (or AI agent) should be able to implement the fixes using this guide plus the referenced code paths.

## Files Structure

```
v8/
├── README.md                                   # This file – implementation guide and context
└── meal-plan-generation-investigation-and-fixes.md  # Root-cause analysis, proposed fixes, comparisons
```

## Implementation Approach

### Goal
Make Meal Plan generation fast, cancellable, and reliable by reducing unnecessary network calls, adding safe parallelism, and keeping the main thread responsive.

### Work Bundles (recommended order)
1. P0 – Disable Detail Prefetch in Meal Plan Path (low-risk, highest impact)
2. P1 – Parallelize Per‑Meal Generation (Lunch/Dinner concurrent with cap)
3. P1 – Keep Main Thread Free (avoid awaiting the whole task on MainActor)
4. P2 – Offload PlanStore Merge/Encode off MainActor
5. P2 – (Optional) Decouple Prefetch into a dedicated service for future tuning

## Technical Context & Architecture

### Project Structure (relevant parts)
```
Features/
└── RecipeGenerator/
    ├── RecipeGeneratorView.swift         # Generate button & UI state
    └── RecipeGeneratorViewModel.swift    # Generation control flow & state
Services/
├── StructuredMealPlanGenerator.swift     # Slot enumeration & per‑meal batching
├── RecipeServiceAdapter.swift            # Idea generation + (currently) detail prefetch
├── RecipeGenerationService.swift         # Entry to structured generation
├── PlanStore.swift                       # Plan merge & persistence (MainActor)
└── GeminiAPIService.swift                # AI detail generation (network)
Utils/
└── MealCutoffManager.swift               # Same‑day meal cutoff logic
```

### Data Flow (Meal Plan)
RecipeGeneratorView → RecipeGeneratorViewModel → RecipeGenerationService.generateStructuredMealPlan → StructuredMealPlanGenerator → RecipeServiceAdapter.generate (per meal) → [assign to slots] → PlanStore.mergeAndSave

### Key Differences vs Quick Mode
- Quick: single batch + up to 3 detail prefetch (kept enabled)
- Meal Plan: multiple meal batches; prefetch is the main source of extra latency and is disabled after P0

## Engineering Standards

- Performance Targets
  - Meal Plan generation perceived wait time: reduced by 50%+
  - Total AI calls (example with Lunch+Dinner, 5 days): 4 calls after P0 (was up to 10)
- Concurrency & Safety
  - Use TaskGroup to parallelize per‑meal generation; set a small concurrency cap if external APIs limit throughput
  - Ensure thread safety in services used concurrently
- Cancellation
  - Honor Task.isCancelled checks around network awaits; ensure Cancel button interrupts promptly
- Error Handling
  - Graceful fallbacks when ideas are insufficient; never crash on empty pools
  - Surface partial‑success states without blocking other meal types

## Implementation Guidelines

1) Disable detail prefetch for Meal Plan
- Add `prefetchDetails: Bool = true` parameter to `RecipeServiceAdapter.generate(...)`
- From `StructuredMealPlanGenerator`, call with `prefetchDetails: false`

2) Parallelize per‑meal generation
- In `StructuredMealPlanGenerator.generatePlan`, run each meal type generation in a TaskGroup and merge results; consider a small cap if needed

3) Keep MainActor responsive
- In `RecipeGeneratorViewModel`, do not `await task.value` on MainActor for the whole generation
- Update `viewState` on MainActor only when needed; keep heavy work off the main thread

4) Offload PlanStore work
- Perform plan merge + JSON encode on a background task/actor; finalize writes and UI updates on MainActor

5) (Optional) Prefetcher decoupling
- Extract detail prefetch into a `RecipeDetailPrefetcher` service; keep adapter focused on idea generation

## Validation Commands

```bash
# Build (if using SPM-managed modules)
swift build

# Unit tests (adjust filters to your test targets)
swift test --filter MealPlanGenerationTests

# Full tests
swift test

# Xcode UI tests example (adjust scheme/destination)
xcodebuild test -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 15 Pro'
```

## Test Plan

- Unit
  - Assert Meal Plan path does not invoke detail prefetch; Quick still does
  - Verify per‑meal parallelization returns correct counts and preserves slot assignment
  - PlanStore merge retains favorites and enforces 4‑week retention
- Integration
  - Scenario: 5 days, Lunch 3×@40min, Dinner 4×@60min, family size 5
  - Measure wall‑clock time before/after P0 and after P1; confirm AI call counts drop from up to 10 → 4
  - Ensure Cancel interrupts mid‑generation and UI recovers cleanly

## Success Metrics

- Performance
  - Meal Plan wall‑clock time significantly reduced vs baseline
  - Total AI calls reduced per scenario: up to 10 → 4 (Lunch+Dinner)
- UX
  - Loading remains responsive; Cancel reacts quickly
  - Navigation to Plans tab completes reliably after save
- Correctness
  - Same‑day cutoff honored
  - Plan saved/merged correctly with retention (4 weeks)

## Troubleshooting

- Rate limiting when parallel
  - Reduce concurrency cap; stagger meal starts by a few hundred ms if necessary
- Too few ideas returned
  - Expect retry once, then fallback fill; verify de‑duplication in adapter
- “Missing meals” on Day 1
  - Check same‑day cutoff rules in `MealCutoffManager`
- Expectation of instant details
  - Meal Plan no longer prefetches details; details load on demand in detail view

## Release Checklist

- [ ] P0: Prefetch disabled for Meal Plan only; Quick unchanged
- [ ] P1: Per‑meal parallelization in place with safe cap
- [ ] P1: No MainActor await of full task; UI stays responsive
- [ ] P2: PlanStore heavy work off MainActor
- [ ] Unit + integration tests updated and passing
- [ ] Measured improvements recorded in PR description
- [ ] Documentation updated (this README + investigation doc)

## References

- Root-cause analysis: v8/meal-plan-generation-investigation-and-fixes.md
- Key code paths:
  - Features/RecipeGenerator/RecipeGeneratorView.swift
  - Features/RecipeGenerator/RecipeGeneratorViewModel.swift
  - Services/StructuredMealPlanGenerator.swift
  - Services/RecipeServiceAdapter.swift
  - Services/RecipeGenerationService.swift
  - Services/PlanStore.swift
  - Utils/MealCutoffManager.swift

