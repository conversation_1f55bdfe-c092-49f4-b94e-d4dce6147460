import SwiftUI
import PhotosUI

/// Profile view that directly shows sign-in when not authenticated
/// and preferences management when authenticated
struct ProfileView: View {
    
    @Environment(AuthenticationService.self) var authService: AuthenticationService
    @Environment(NavigationCoordinator.self) var coordinator: NavigationCoordinator
    
    // State for sign-in and account sheets
    @State(initialValue: false) private var showingSignInSheet: Bool
    @State(initialValue: false) private var showingAccountView: Bool
    
    var body: some View {
        Group {
            if authService.isAuthenticated {
                authenticatedView
            } else {
                // Show sign-in prompt
                signInPromptView
            }
        }
        .sheet(isPresented: $showingSignInSheet) { SignInView() }
        .sheet(isPresented: $showingAccountView) { ProfileAccountView() }
        .task(id: authService.isAuthenticated) {
            // Auto-show sign-in sheet when user taps Profile tab while not authenticated
            if !authService.isAuthenticated && !showingSignInSheet {
                showingSignInSheet = true
            }
        }
    }
    
    // MARK: - Sign-In Prompt View
    
    @ViewBuilder
    private var signInPromptView: some View {
        VStack(spacing: 32) {
            Spacer()
            
            VStack(spacing: 16) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 80))
                    .foregroundStyle(.blue.gradient)
                
                VStack(spacing: 8) {
                    Text("Sign In Required")
                        .font(.title2.weight(.semibold))
                        .foregroundStyle(.primary)
                    
                    Text("Sign in to access your preferences and sync across devices")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 32)
                }
            }
            
            Button("Sign In") {
                showingSignInSheet = true
            }
            .buttonStyle(.borderedProminent)
            .font(.headline)
            .padding(.horizontal, 40)
            
            Spacer()
        }
        .padding()
        .navigationTitle("Profile")
        .navigationBarTitleDisplayMode(.large)
    }
}

// MARK: - Authenticated Content

private extension ProfileView {
    @ViewBuilder
    var authenticatedView: some View {
        PreferencesEditView()
            .safeAreaInset(edge: .bottom) {
                accountSection
            }
    }

    @ViewBuilder
    var accountSection: some View {
        VStack(spacing: 12) {
            Divider()

            Button {
                showingAccountView = true
            } label: {
                HStack(spacing: 16) {
                    Image(systemName: "person.circle")
                        .font(.title2)
                        .foregroundStyle(.blue)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Account")
                            .font(.headline)
                            .foregroundStyle(.primary)
                        Text("Manage your account settings")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.caption.weight(.semibold))
                        .foregroundStyle(.tertiary)
                }
                .padding(.vertical, 12)
                .padding(.horizontal)
                .background(Color(.secondarySystemBackground), in: RoundedRectangle(cornerRadius: 14))
            }
            .buttonStyle(.plain)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 16)
        .background(Color(.systemGroupedBackground).ignoresSafeArea())
    }
}

// MARK: - Preview

#if DEBUG
#Preview("Not Authenticated") {
    NavigationStack {
        ProfileView()
            .environment(AuthenticationService())
            .environment(NavigationCoordinator())
    }
}

#Preview("Authenticated") {
    let authService = AuthenticationService()
    // Mock authenticated state for preview
    return NavigationStack {
        ProfileView()
            .environment(authService)
            .environment(NavigationCoordinator())
    }
}
#endif
