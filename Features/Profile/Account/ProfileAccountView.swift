import SwiftUI
import FirebaseAuth

struct ProfileAccountView: View {
    @Environment(AuthenticationService.self) private var authService
    @Environment(\.dismiss) private var dismiss

    @StateObject private var networkService = OptimizedNetworkService.shared
    @StateObject private var resetManager = AccountResetManager.shared
    @StateObject private var reauthCoordinator = ReauthenticationCoordinator(authService: ServiceContainer.shared.authenticationService)

    @State private var showingDeleteConfirmation = false
    @State private var showingResetConfirmation = false
    @State private var showingChangePassword = false
    @State private var showingUpdateEmail = false
    @State private var showingEditProfile = false
    @State private var showingSignOutAlert = false
    @State private var isPerformingSignOut = false
    @State private var activeAlert: AccountAlert?

    private let telemetry = ServiceContainer.shared.telemetryService

    var body: some View {
        NavigationView {
            List {
                accountHeaderSection
                accountInfoSection
                securitySection

                if resetManager.isResetting {
                    resetProgressSection
                }
            }
            .navigationTitle("Account")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Done") { dismiss() }
                }
            }
        }
        .sheet(isPresented: $showingChangePassword) {
            ChangePasswordView()
        }
        .sheet(isPresented: $showingUpdateEmail) {
            UpdateEmailView()
        }
        .sheet(isPresented: $showingEditProfile) {
            EditProfileView()
        }
        .sheet(isPresented: $reauthCoordinator.isPresented) {
            ReauthenticationView(coordinator: reauthCoordinator)
        }
        .alert(item: $activeAlert) { alert in
            Alert(
                title: Text(alert.title),
                message: Text(alert.message),
                dismissButton: .default(Text("OK"))
            )
        }
        .confirmationDialog("Reset Account", isPresented: $showingResetConfirmation, titleVisibility: .visible) {
            Button("Reset Account", role: .destructive) {
                Task { await resetAccount() }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This will clear all data stored on this device only. You'll remain signed in to your Firebase account.")
        }
        .confirmationDialog("Delete Account", isPresented: $showingDeleteConfirmation, titleVisibility: .visible) {
            Button("Delete Account", role: .destructive) {
                Task { await deleteAccount() }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This permanently deletes your account and local data. This action cannot be undone.")
        }
        .alert("Sign Out", isPresented: $showingSignOutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                Task { await signOut() }
            }
        } message: {
            Text("Are you sure you want to sign out?")
        }
    }
}

// MARK: - Sections

private extension ProfileAccountView {
    @ViewBuilder
    var accountHeaderSection: some View {
        Section {
            HStack(spacing: 16) {
                AsyncImage(url: authService.currentUser?.photoURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 40))
                        .foregroundStyle(.blue.gradient)
                }
                .frame(width: 60, height: 60)
                .clipShape(Circle())

                VStack(alignment: .leading, spacing: 6) {
                    Text("Signed in as")
                        .font(.caption)
                        .foregroundStyle(.secondary)

                    Text(userDisplayText)
                        .font(.headline)
                        .foregroundStyle(.primary)
                        .accessibilityIdentifier("account_user_display")

                    if let user = authService.currentUser {
                        HStack(spacing: 6) {
                            Circle()
                                .fill(user.isEmailVerified ? Color.green : Color.orange)
                                .frame(width: 8, height: 8)

                            Text(user.isEmailVerified ? "Verified" : "Unverified")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                    }
                }

                Spacer()
            }
            .padding(.vertical, 8)
        }
    }

    @ViewBuilder
    var accountInfoSection: some View {
        Section("Account Information") {
            AccountActionRow(
                icon: "person.circle",
                title: "Edit Profile",
                subtitle: "Update display name",
                action: { showingEditProfile = true }
            )
            .disabled(!userCapabilities.canUpdateProfile)

            AccountActionRow(
                icon: "envelope",
                title: "Update Email",
                subtitle: currentUserEmail ?? "No email on file",
                action: { showingUpdateEmail = true }
            )
            .disabled(!userCapabilities.canUpdateEmail || !networkService.isOnline)

            if let user = authService.currentUser, !user.isEmailVerified {
                AccountActionRow(
                    icon: "checkmark.seal",
                    title: "Verify Email",
                    subtitle: "Send verification email",
                    action: { Task { await sendEmailVerification() } }
                )
                .disabled(!networkService.isOnline)
            }
        }
    }

    @ViewBuilder
    var securitySection: some View {
        Section("Security & Data") {
            if userCapabilities.canChangePassword {
                AccountActionRow(
                    icon: "key",
                    title: "Change Password",
                    subtitle: "Update your password",
                    action: { showingChangePassword = true }
                )
                .disabled(!networkService.isOnline)
            } else if let email = currentUserEmail {
                AccountActionRow(
                    icon: "key",
                    title: "Reset Password",
                    subtitle: "Send reset link to \(email)",
                    action: { Task { await sendPasswordReset(to: email) } }
                )
                .disabled(!networkService.isOnline)
            }

            AccountActionRow(
                icon: "arrow.clockwise",
                title: "Reset Account",
                subtitle: "Clear data on this device",
                action: { showingResetConfirmation = true }
            )
            .disabled(resetManager.isResetting)

            AccountActionRow(
                icon: "trash",
                title: "Delete Account",
                subtitle: "Permanently remove account",
                isDestructive: true,
                action: { showingDeleteConfirmation = true }
            )
            .disabled(!networkService.isOnline)

            AccountActionRow(
                icon: "rectangle.portrait.and.arrow.right",
                title: "Sign Out",
                subtitle: "Sign out of this device",
                action: { showingSignOutAlert = true }
            )
            .disabled(isPerformingSignOut)
        }
    }

    @ViewBuilder
    var resetProgressSection: some View {
        Section("Reset Status") {
            VStack(alignment: .leading, spacing: 8) {
                ProgressView(value: resetManager.resetProgress.progressValue) {
                    Text(resetManager.resetProgress.description)
                }
                .progressViewStyle(.linear)

                if let error = resetManager.resetError {
                    Text(error.localizedDescription)
                        .font(.caption)
                        .foregroundStyle(.red)
                }
            }
            .padding(.vertical, 8)
        }
    }
}

// MARK: - Actions

private extension ProfileAccountView {
    func sendEmailVerification() async {
        guard networkService.isOnline else {
            activeAlert = AccountAlert(title: "Offline", message: "Connect to the internet to send a verification email.")
            return
        }

        let start = Date()

        do {
            try await authService.sendEmailVerification()
            trackAccountEvent(.emailVerificationSent, startedAt: start, error: nil)
            activeAlert = AccountAlert(title: "Verification Sent", message: "Check your inbox to verify your email address.")
        } catch {
            trackAccountEvent(.emailVerificationSent, startedAt: start, error: error)
            activeAlert = AccountAlert(title: "Verification Failed", message: error.localizedDescription)
        }
    }

    func sendPasswordReset(to email: String) async {
        guard networkService.isOnline else {
            activeAlert = AccountAlert(title: "Offline", message: "Connect to the internet to send a password reset email.")
            return
        }

        let start = Date()

        do {
            try await authService.sendPasswordReset(email: email)
            trackAccountEvent(.passwordResetEmailSent, startedAt: start, error: nil)
            activeAlert = AccountAlert(title: "Email Sent", message: "Password reset instructions were sent to \(email).")
        } catch {
            trackAccountEvent(.passwordResetEmailSent, startedAt: start, error: error)
            activeAlert = AccountAlert(title: "Reset Failed", message: error.localizedDescription)
        }
    }

    func resetAccount() async {
        guard !resetManager.isResetting else { return }
        let start = Date()

        do {
            try await resetManager.resetAccount()
            trackAccountEvent(.resetConfirmed, startedAt: start, error: nil)
            activeAlert = AccountAlert(title: "Account Reset", message: "Local data has been cleared on this device.")
        } catch {
            trackAccountEvent(.resetConfirmed, startedAt: start, error: error)
            activeAlert = AccountAlert(title: "Reset Failed", message: error.localizedDescription)
        }
    }

    func deleteAccount() async {
        guard networkService.isOnline else {
            activeAlert = AccountAlert(title: "Offline", message: "Connect to the internet to delete your account.")
            return
        }

        let start = Date()

        do {
            try await authService.deleteCurrentUser()
            trackAccountEvent(.deleteConfirmed, startedAt: start, error: nil)
            dismiss()
        } catch {
            trackAccountEvent(.deleteConfirmed, startedAt: start, error: error)
            activeAlert = AccountAlert(title: "Delete Failed", message: error.localizedDescription)
        }
    }

    func signOut() async {
        guard !isPerformingSignOut else { return }
        isPerformingSignOut = true
        let start = Date()

        do {
            try authService.signOut()
            trackAccountEvent(.signOut, startedAt: start, error: nil)
            dismiss()
        } catch {
            trackAccountEvent(.signOut, startedAt: start, error: error)
            activeAlert = AccountAlert(title: "Sign Out Failed", message: error.localizedDescription)
        }

        isPerformingSignOut = false
    }

    func trackAccountEvent(_ event: TelemetryService.AccountEvent, startedAt: Date, error: Error?) {
        AccountTelemetryHelper.track(
            event,
            authService: authService,
            telemetry: telemetry,
            startedAt: startedAt,
            error: error
        )
    }
}

// MARK: - Helpers

private extension ProfileAccountView {
    var userDisplayText: String {
        if let user = authService.currentUser {
            if let displayName = user.displayName, !displayName.isEmpty {
                return displayName
            }
            if let email = user.email, !email.isEmpty {
                return email
            }
        }
        return "Unknown User"
    }

    var currentUserEmail: String? {
        authService.currentUser?.email
    }

    var userCapabilities: UserCapabilities {
        guard let user = authService.currentUser else {
            return .init(canChangePassword: false, canUpdateEmail: false, canUpdateProfile: false)
        }

        let providerIDs = Set(user.providerData.map(\.providerID))
        let canChangePassword = providerIDs.contains("password")
        let canUpdateEmail = !user.isAnonymous && user.email != nil
        let canUpdateProfile = !user.isAnonymous

        return .init(
            canChangePassword: canChangePassword,
            canUpdateEmail: canUpdateEmail,
            canUpdateProfile: canUpdateProfile
        )
    }

}

// MARK: - Supporting Types

private struct AccountAlert: Identifiable {
    let id = UUID()
    let title: String
    let message: String
}

private struct UserCapabilities {
    let canChangePassword: Bool
    let canUpdateEmail: Bool
    let canUpdateProfile: Bool
}

struct AccountActionRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let isDestructive: Bool
    let action: () -> Void

    init(
        icon: String,
        title: String,
        subtitle: String,
        isDestructive: Bool = false,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.isDestructive = isDestructive
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundStyle(isDestructive ? .red : .blue)
                    .frame(width: 28, height: 28)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .foregroundStyle(isDestructive ? .red : .primary)
                    Text(subtitle)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .lineLimit(1)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundStyle(.tertiary)
            }
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }
}
